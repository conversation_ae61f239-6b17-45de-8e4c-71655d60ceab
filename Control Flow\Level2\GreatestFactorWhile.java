import java.util.Scanner;

public class GreatestFactorWhile {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a positive integer: ");
        int number = scanner.nextInt();
        
        if (number <= 0) {
            System.out.println("Please enter a positive integer");
        } else {
            int greatestFactor = 1;
            int counter = number - 1;
            
            while (counter >= 1) {
                if (number % counter == 0) {
                    greatestFactor = counter;
                    break;
                }
                counter--;
            }
            
            System.out.println("Greatest factor of " + number + " beside itself is: " + greatestFactor);
        }
        
        scanner.close();
    }
}
