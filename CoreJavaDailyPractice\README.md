# Core Java Daily Practice

Welcome to your Core Java Daily Practice repository! This is organized to help you practice Java concepts systematically.

## Structure

### 📁 Fundamentals
- **Basics**: Variables, Data Types, Operators
- **ControlFlow**: Loops, Conditionals, Switch
- **Methods**: Method overloading, recursion
- **Arrays**: 1D, 2D arrays, array operations

### 📁 OOP (Object-Oriented Programming)
- **Classes**: Class creation, constructors
- **Inheritance**: Extends, super keyword
- **Polymorphism**: Method overriding, runtime polymorphism
- **Abstraction**: Abstract classes, interfaces
- **Encapsulation**: Access modifiers, getters/setters

### 📁 Advanced
- **Collections**: ArrayList, HashMap, LinkedList, etc.
- **ExceptionHandling**: Try-catch, custom exceptions
- **FileIO**: File reading/writing operations
- **Multithreading**: Thread creation, synchronization
- **Generics**: Generic classes and methods

### 📁 ProblemSolving
- **Algorithms**: Sorting, searching algorithms
- **DataStructures**: Stack, Queue, LinkedList implementations
- **Patterns**: Design patterns implementation
- **CodingChallenges**: Daily coding problems

### 📁 Projects
- **MiniProjects**: Small applications
- **Practice**: Hands-on projects

## Daily Practice Guidelines

1. **Pick a topic** from any folder
2. **Read the concept** in the README of that folder
3. **Practice the examples** provided
4. **Solve the exercises** 
5. **Create your own variations**
6. **Document your learning** in daily logs

## How to Use

1. Navigate to any topic folder
2. Start with `README.md` for concept explanation
3. Run the example files
4. Complete the exercises
5. Check solutions when needed

## Progress Tracking

- [ ] Fundamentals Complete
- [ ] OOP Complete  
- [ ] Advanced Complete
- [ ] Problem Solving Complete
- [ ] Projects Complete

Happy Coding! 🚀
